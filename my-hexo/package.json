{"name": "hexo-site", "version": "0.0.0", "private": true, "hexo": {"version": "6.3.0"}, "dependencies": {"cheerio": "^1.0.0", "gulp": "^5.0.0", "gulp-html-minifier-terser": "^7.1.0", "hexo": "^6.3.0", "hexo-abbrlink": "^2.2.1", "hexo-filter-nofollow": "^2.0.2", "hexo-generator-archive": "^2.0.0", "hexo-generator-category": "^2.0.0", "hexo-generator-index": "^3.0.0", "hexo-generator-sitemap": "^3.0.1", "hexo-generator-tag": "^2.0.0", "hexo-renderer-ejs": "^2.0.0", "hexo-renderer-marked": "^6.0.0", "hexo-renderer-pug": "^3.0.0", "hexo-renderer-stylus": "^2.1.0", "hexo-safego": "^2.2.2", "hexo-server": "^3.0.0", "hexo-submit-urls-to-search-engine": "^2.1.0", "hexo-wordcount": "^6.0.1"}, "scripts": {"dev": "hexo server -p $PORT", "build": "hexo generate"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-htmlclean": "^2.7.22", "gulp-uglify": "^3.0.2"}}