div
  script(src=url_for(theme.asset.utils))
  script(src=url_for(theme.asset.main))

  if theme.translate.enable
    script(src=url_for(theme.asset.translate))

  if theme.lightbox
    script(src=url_for(theme.asset[theme.lightbox]))

  if theme.instantpage
    script(src=url_for(theme.asset.instantpage), type='module')

  if theme.lazyload.enable && !theme.lazyload.native
    script(src=url_for(theme.asset.lazyload))

  if theme.snackbar.enable
    script(src=url_for(theme.asset.snackbar))

  .js-pjax
    if needLoadCountJs
      != partial("includes/third-party/card-post-count/index", {}, { cache: true })

    if loadSubJs
      include ./third-party/subtitle.pug

    include ./third-party/math/index.pug
    include ./third-party/abcjs/index.pug

    if commentsJsLoad
      include ./third-party/comments/js.pug

  != partial("includes/third-party/prismjs", {}, { cache: true })

  if theme.aside.enable && theme.aside.card_newest_comments.enable
    if theme.pjax.enable || (globalPageType !== 'post' && page.aside !== false)
      != partial("includes/third-party/newest-comments/index", {}, { cache: true })

  != fragment_cache('injectBottom', function(){return injectHtml(theme.inject.bottom)})

  != partial("includes/third-party/effect", {}, { cache: true })
  != partial("includes/third-party/chat/index", {}, { cache: true })

  if theme.aplayerInject && theme.aplayerInject.enable
    if theme.pjax.enable || theme.aplayerInject.per_page || page.aplayer
      include ./third-party/aplayer.pug

  if theme.pjax.enable
    != partial("includes/third-party/pjax", {}, { cache: true })

  if theme.umami_analytics.enable
    != partial("includes/third-party/umami_analytics", {}, { cache: true })

  if theme.busuanzi.site_uv || theme.busuanzi.site_pv || theme.busuanzi.page_pv
    script(async data-pjax src= theme.asset.busuanzi || '//busuanzi.ibruce.info/busuanzi/2.3/busuanzi.pure.mini.js')

  != partial('includes/third-party/search/index', {}, { cache: true })