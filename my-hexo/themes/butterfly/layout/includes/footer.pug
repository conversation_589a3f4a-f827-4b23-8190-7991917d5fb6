#footer-wrap
  if theme.footer.owner.enable
    - const currentYear = new Date().getFullYear()
    - const sinceYear = theme.footer.owner.since
    .copyright
      if sinceYear && sinceYear != currentYear
        != `&copy;${sinceYear} - ${currentYear} By ${config.author}`
      else
        != `&copy;${currentYear} By ${config.author}`
  if theme.footer.copyright
    - const v = getVersion()
    .framework-info
      span= _p('footer.framework') + ' '
      a(href='https://hexo.io')= `Hexo ${v.hexo}`
      span.footer-separator |
      span= _p('footer.theme') + ' '
      a(href='https://github.com/jerryc127/hexo-theme-butterfly')= `Butterfly ${v.theme}`
  if theme.footer.custom_text
    .footer_custom_text!= theme.footer.custom_text