extends includes/layout.pug

block content
  #post
    if top_img === false
      include includes/header/post-info.pug

    article#article-container.container.post-content
      if theme.noticeOutdate.enable && page.noticeOutdate !== false
        include includes/post/outdate-notice.pug
      else
        !=page.content
    include includes/post/post-copyright.pug
    .tag_share
      if (page.tags.length > 0 && theme.post_meta.post.tags)
        .post-meta__tag-list
          each item, index in page.tags.data
            a(href=url_for(item.path)).post-meta__tags #[=item.name]
      include includes/third-party/share/index.pug

    if theme.reward.enable && theme.reward.QR_code
      !=partial('includes/post/reward', {}, {cache: true})

    //- ad
    if theme.ad && theme.ad.post
      .ads-wrap!=theme.ad.post

    if theme.post_pagination
      include includes/pagination.pug
    if theme.related_post && theme.related_post.enable
      != related_posts(page,site.posts)

    if page.comments !== false && theme.comments.use
      - var commentsJsLoad = true
      !=partial('includes/third-party/comments/index', {}, {cache: true})
