if hexo-config('darkmode.enable') || hexo-config('display_mode') == 'dark'
  [data-theme='dark']
    --global-bg: darken(#121212, 2)
    --font-color: alpha(#FFFFFF, .7)
    --hr-border: alpha(#FFFFFF, .4)
    --hr-before-color: alpha(#FFFFFF, .7)
    --search-bg: #121212
    --search-input-color: alpha(#FFFFFF, .7)
    --search-a-color: alpha(#FFFFFF, .7)
    --preloader-bg: darken(#121212, 2)
    --preloader-color: alpha(#FFFFFF, .7)
    --tab-border-color: #2c2c2c
    --tab-button-bg: #2c2c2c
    --tab-button-color: alpha(#FFFFFF, .7)
    --tab-button-hover-bg: lighten(#121212, 15)
    --tab-button-active-bg: #121212
    --card-bg: #121212
    --sidebar-bg: #121212
    --sidebar-menu-bg: lighten(#121212, 5)
    --btn-hover-color: lighten(#121212, 40)
    --btn-color: alpha(#FFFFFF, .7)
    --btn-bg: lighten(#121212, 5)
    --text-bg-hover: lighten(#121212, 15)
    --light-grey: alpha(#FFFFFF, .7)
    --dark-grey: alpha(#FFFFFF, .2)
    --white: alpha(#FFFFFF, .9)
    --text-highlight-color: alpha(#FFFFFF, .9)
    --blockquote-color: alpha(#FFFFFF, .7)
    --blockquote-bg: lighten(#121212, 10)
    --reward-pop: lighten(#121212, 10)
    --toc-link-color: alpha(#FFFFFF, .6)
    --scrollbar-color: lighten(#121212, 25)
    --timeline-bg: lighten(#121212, 5)
    --zoom-bg: #121212
    --mark-bg: alpha($dark-black, .6)

    #web_bg:before
      position: absolute
      width: 100%
      height: 100%
      background-color: alpha($dark-black, .7)
      content: ''

    .container
      code
        background: #2c2c2c

      pre > code
        background: lighten(#121212, 2)

      figure.highlight
        box-shadow: none

      .note
        code
          background: $code-background

      .aplayer
        filter: brightness(.8)

      kbd
        border-color: #696969
        background-color: #525252
        color: #e2f1ff

    // 頭部
    #page-header
      &.nav-fixed > #nav,
      &.not-top-img > #nav
        background: alpha(#121212, .8)
        box-shadow: 0 5px 6px -5px rgba(133, 133, 133, 0)

    #post-comment
      .comment-switch
        if hexo-config('comments.text')
          background: #2c2c2c !important

        #switch-btn
          filter: brightness(.8)

    // note
    if hexo-config('note.style') == 'modern' || hexo-config('note.style') == 'flat'
      .note
        filter: brightness(.8)

    // hide-tags
    .hide-button,
    .btn-beautify,
    .hl-label,
    #post-outdate-notice,
    .error-img,
    .container iframe,
    .gist,
    .ads-wrap
      filter: brightness(.8)

    img
      if hexo-config('lazyload.enable') && hexo-config('lazyload.blur') && !hexo-config('lazyload.placeholder')
        filter: blur(0) brightness(.8)
      else
        filter: brightness(.8)

    #aside-content .aside-list > .aside-list-item:not(:last-child)
      border-bottom: 1px dashed alpha(#FFFFFF, .1)

    // Gitalk
    #gitalk-container
      filter: brightness(.8)

      svg
        fill: alpha(#FFFFFF, .9) !important

    // Disqusjs 反代模式下的適配
    #disqusjs
      #dsqjs
        &:hover,
        &:focus,
        .dsqjs-tab-active,
        .dsqjs-no-comment
          color: alpha(#FFFFFF, .7)

        .dsqjs-order-label
          background-color: lighten(#121212, 5)

        .dsqjs-post-body
          color: alpha(#FFFFFF, .7)

          code,
          pre
            background: #2c2c2c

          blockquote
            color: alpha(#FFFFFF, .7)

    #artitalk_main #lazy
      background: #121212

    #operare_artitalk .c2
      background: #121212

    #card-toc
      +maxWidth900()
        background: lighten(#121212, 5)

    // artalk
    .artalk.atk-dark-mode,
    .atk-layer-wrap.atk-dark-mode
      --at-color-font: alpha(#FFFFFF, .7)
      --at-color-meta: alpha(#FFFFFF, .7)
      --at-color-grey: alpha(#FFFFFF, .7)
    
    .atk-send-btn,
    .atk-badge
      color: alpha(#FFFFFF, .7) !important

    // waline
    #waline-wrap
      --waline-color: alpha(#FFFFFF, .7)
      --waline-dark-grey: alpha(#FFFFFF, .7)
      --waline-info-color: alpha(#FFFFFF, .5)