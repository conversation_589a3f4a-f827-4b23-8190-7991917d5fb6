.search-dialog
  position: fixed
  top: 10%
  left: 50%
  z-index: 1001
  display: none
  margin-left: -300px
  padding: 20px
  width: 600px
  background: var(--search-bg)
  --search-height: 100vh
  addBorderRadius(8)

  +maxWidth768()
    top: 0
    left: 0
    margin: 0
    width: 100%
    height: 100%
    border-radius: 0

  .search-nav
    margin: 0 0 14px
    color: $search-color
    font-size: 1.4em
    line-height: 1

    .search-dialog-title
      margin-right: 10px

    .search-close-button
      float: right
      color: $grey
      transition: color .2s ease-in-out

      &:hover
        color: $search-color
  
  hr
    margin: 15px auto
    @extend .custom-hr

#search-mask
  position: fixed
  top: 0
  right: 0
  bottom: 0
  left: 0
  z-index: 1000
  display: none
  background: rgba($dark-black, .6)

if hexo-config('search.use') == 'algolia_search'
  @require 'algolia'
else if hexo-config('search.use') == 'local_search'
  @require 'local-search'