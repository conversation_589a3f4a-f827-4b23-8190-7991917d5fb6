#sidebar
  #menu-mask
    position: fixed
    z-index: 102
    display: none
    width: 100%
    height: 100%
    background: alpha($dark-black, .8)

  #sidebar-menus
    position: fixed
    top: 0
    right: -($sidebar-width)
    z-index: 103
    overflow-x: hidden
    overflow-y: scroll
    padding-left: 5px
    width: $sidebar-width
    height: 100%
    background: var(--sidebar-bg)
    transition: all .5s

    &.open
      transform: translate3d(-100%, 0, 0)

    & > .avatar-img
      margin: 20px auto

    .site-data
      padding: 0 10px

    hr
      margin: 20px auto

    .menus_items
      margin: 20px
      padding: 15px
      background: var(--sidebar-menu-bg)
      box-shadow: 0 0 1px 1px rgba(7, 17, 27, .05)
      addBorderRadius(10)

      .site-page
        @extend .limit-one-line
        position: relative
        display: block
        margin: 4px 0
        padding: 2px 23px 2px 15px
        color: var(--font-color)
        font-size: 1.15em
        cursor: pointer
        addBorderRadius(6)

        &:hover
          background: var(--text-bg-hover)
          color: var(--white)

        i:first-child
          width: 15%
          text-align: left

        &.group
          & > i:last-child
            position: absolute
            top: .6em
            right: 10px
            transition: transform .3s

          &.hide
            & > i:last-child
              transform: rotate(90deg)

            & + .menus_item_child
              display: none

      .menus_item_child
        margin: 0
        padding-left: 25px
        list-style: none