#rightside
  position: fixed
  right: -48px
  bottom: $rightside-bottom
  z-index: 100
  opacity: 0
  transition: all .5s

  &.rightside-show
    opacity: .8
    transform: translate(-58px, 0)

  #rightside-config-hide
    height: 0
    opacity: 0
    transition: transform .4s
    transform: translate(45px, 0)

    &.show
      height: auto
      opacity: 1
      transform: translate(0, 0)

    &.status
      height: auto
      opacity: 1

  & > div
    & > button,
    & > a
      display: block
      margin-bottom: 5px
      width: w = 35px
      height: w
      background-color: var(--btn-bg)
      color: var(--btn-color)
      text-align: center
      font-size: 16px
      line-height: w
      addBorderRadius(5)

      &:hover
        background-color: var(--btn-hover-color)

  #mobile-toc-button
    display: none

    +maxWidth900()
      display: block

  +maxWidth900()
    #hide-aside-btn
      display: none

  if hexo-config('rightside_scroll_percent')
    #go-up
      .scroll-percent
        display: none

      &.show-percent
        .scroll-percent
          display: block

          & + i
            display: none

      &:hover
        .scroll-percent
          display: none

          & + i
            display: block
