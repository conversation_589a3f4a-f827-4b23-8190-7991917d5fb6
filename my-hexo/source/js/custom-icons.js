document.addEventListener('DOMContentLoaded', function() {
  // 替换Linux图标
  const linuxIcons = document.querySelectorAll('.iconfont.icon-linux');
  linuxIcons.forEach(icon => {
    const imgElement = document.createElement('img');
    imgElement.src = '/image/icons/linux.svg';
    imgElement.style.width = '1.2em';
    imgElement.style.height = '1.2em';
    imgElement.style.verticalAlign = 'middle';
    icon.parentNode.replaceChild(imgElement, icon);
  });

  // 替换微信图标
  const weixinIcons = document.querySelectorAll('.iconfont.icon-weixin');
  weixinIcons.forEach(icon => {
    const imgElement = document.createElement('img');
    imgElement.src = '/image/icons/微信.svg';
    imgElement.style.width = '1.2em';
    imgElement.style.height = '1.2em';
    imgElement.style.verticalAlign = 'middle';
    icon.parentNode.replaceChild(imgElement, icon);
  });

  // 替换telegram图标
  const telegramIcons = document.querySelectorAll('.iconfont.icon-telegram');
  telegramIcons.forEach(icon => {
    const imgElement = document.createElement('img');
    imgElement.src = '/image/icons/telegram.svg';
    imgElement.style.width = '1.2em';
    imgElement.style.height = '1.2em';
    imgElement.style.verticalAlign = 'middle';
    icon.parentNode.replaceChild(imgElement, icon);
  });

  // 替换bilibili图标
  const bilibiliIcons = document.querySelectorAll('.iconfont.icon--bilibili');
  bilibiliIcons.forEach(icon => {
    const imgElement = document.createElement('img');
    imgElement.src = '/image/icons/bilibili.svg';
    imgElement.style.width = '1.2em';
    imgElement.style.height = '1.2em';
    imgElement.style.verticalAlign = 'middle';
    icon.parentNode.replaceChild(imgElement, icon);
  });
}); 