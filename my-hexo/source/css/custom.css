/* 确保样式被加载 */
@import url('data:text/css;charset=utf-8,');

:root {
  --light_bg_color: rgba(255, 255, 255, 0.8);
  --font-color: #4c4948;
  --june-border: #e3e8f7;
  --calendar-date-color: #E68282; /* 日历日期颜色 */
}

/* 暗黑模式下的变量 */
html[data-theme='dark'] {
  --june-border: #42444a;
  --calendar-date-color: #f2b94b; /* 暗黑模式下的日历日期颜色 */
}

/* 日历卡片大日期颜色 */
#calendar-date {
  color: var(--calendar-date-color) !important;
}

/* 右侧小日历当前日期颜色 */
#calendar-main a.now {
  background: var(--calendar-date-color) !important;
}

#recent-posts>.recent-post-item,.layout_page>div:first-child:not(.recent-posts),.layout_post>#page,.layout_post>#post,.read-mode .layout_post>#post {
    background: var(--light_bg_color);
}

#aside-content .card-widget {
    background: var(--light_bg_color);
}

/* 亮色模式下的全局背景渐变 */
html[data-theme='light'] body #web_bg {
    background: linear-gradient(90deg,rgba(247,149,51,.1),rgba(243,112,85,.1) 15%,rgba(239,78,123,.1) 30%,rgba(161,102,171,.1) 44%,rgba(80,115,184,.1) 58%,rgba(16,152,173,.1) 72%,rgba(7,179,155,.1) 86%,rgba(109,186,130,.1)) !important;
    background-attachment: fixed !important;
    background-size: cover !important;
    background-repeat: no-repeat !important;
    z-index: -999 !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    left: 0 !important;
}

/* 暗黑模式下的全局背景渐变 - 使用更深的颜色 */
html[data-theme='dark'] body #web_bg {
    background: linear-gradient(90deg,rgba(247,149,51,.05),rgba(243,112,85,.05) 15%,rgba(239,78,123,.05) 30%,rgba(161,102,171,.05) 44%,rgba(80,115,184,.05) 58%,rgba(16,152,173,.05) 72%,rgba(7,179,155,.05) 86%,rgba(109,186,130,.05)) !important;
    background-attachment: fixed !important;
    background-size: cover !important;
    background-repeat: no-repeat !important;
    z-index: -999 !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    left: 0 !important;
}

/* 亮色模式下直接设置页面背景 */
html[data-theme='light'] {
    background: linear-gradient(90deg,rgba(247,149,51,.1),rgba(243,112,85,.1) 15%,rgba(239,78,123,.1) 30%,rgba(161,102,171,.1) 44%,rgba(80,115,184,.1) 58%,rgba(16,152,173,.1) 72%,rgba(7,179,155,.1) 86%,rgba(109,186,130,.1)) !important;
    background-attachment: fixed !important;
    background-size: cover !important;
    background-repeat: no-repeat !important;
}

/* 暗黑模式下直接设置页面背景 */
html[data-theme='dark'] {
    background: #121212 !important; /* 使用主题原有的暗色背景 */
}

/* 暗黑模式下的卡片背景 */
html[data-theme='dark'] #recent-posts>.recent-post-item,
html[data-theme='dark'] .layout_page>div:first-child:not(.recent-posts),
html[data-theme='dark'] .layout_post>#page,
html[data-theme='dark'] .layout_post>#post,
html[data-theme='dark'] .read-mode .layout_post>#post,
html[data-theme='dark'] #aside-content .card-widget {
    background: var(--card-bg); /* 使用主题变量 */
}

/* 增强暗黑模式下的文字亮度 */
html[data-theme='dark'] {
    --font-color: rgba(255, 255, 255, 0.9) !important;
    --white: rgba(255, 255, 255, 0.95) !important;
    --text-highlight-color: rgba(255, 255, 255, 0.95) !important;
    --light-grey: rgba(255, 255, 255, 0.85) !important;
}

/* 暗黑模式下的标签、导航和侧边栏文字颜色增强 */
html[data-theme='dark'] #nav a,
html[data-theme='dark'] #nav .site-page,
html[data-theme='dark'] #aside-content .card-info-title,
html[data-theme='dark'] #aside-content .card-info-data .headline,
html[data-theme='dark'] #aside-content .card-info-data .length-num,
html[data-theme='dark'] .card-info-social-icons .social-icon,
html[data-theme='dark'] .card-widget .item-headline,
html[data-theme='dark'] #aside-content .card-archives ul.card-archive-list > .card-archive-list-item a,
html[data-theme='dark'] #aside-content .card-categories ul.card-category-list > .card-category-list-item a,
html[data-theme='dark'] .tag-cloud-title,
html[data-theme='dark'] .tag-cloud a,
html[data-theme='dark'] #nav #blog-info #site-title,
html[data-theme='dark'] #nav #blog-info #site-subtitle,
html[data-theme='dark'] .post-meta,
html[data-theme='dark'] .article-sort-item-title,
html[data-theme='dark'] .article-meta-wrap,
html[data-theme='dark'] #post-info .post-title,
html[data-theme='dark'] h1.post-title {
    color: rgba(255, 255, 255, 0.95) !important;
}

/* 暗黑模式下的标签云效果增强 */
html[data-theme='dark'] .tag-cloud a:hover {
    color: #fff !important;
    background: #49b1f5 !important;
}

/* 暗黑模式下侧边栏标题和图标增强 */
html[data-theme='dark'] #aside-content .item-headline span,
html[data-theme='dark'] #aside-content .item-headline i {
    color: rgba(255, 255, 255, 0.95) !important;
}

/* 暗黑模式下文章列表标题增强 */
html[data-theme='dark'] #recent-posts > .recent-post-item > .recent-post-info > .article-title {
    color: rgba(255, 255, 255, 0.95) !important;
}

/* 暗黑模式下分类和标签链接文字颜色 */
html[data-theme='dark'] .article-sort-item a:not(.article-sort-item-img),
html[data-theme='dark'] .article-sort-item-title,
html[data-theme='dark'] #page .category-list ul a,
html[data-theme='dark'] #page .tag-list ul a {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 暗黑模式下文章内容文字颜色 */
html[data-theme='dark'] #article-container,
html[data-theme='dark'] #article-container p,
html[data-theme='dark'] #article-container ol,
html[data-theme='dark'] #article-container ul,
html[data-theme='dark'] #article-container h1,
html[data-theme='dark'] #article-container h2,
html[data-theme='dark'] #article-container h3,
html[data-theme='dark'] #article-container h4,
html[data-theme='dark'] #article-container h5,
html[data-theme='dark'] #article-container h6 {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 暗黑模式下首页文章摘要颜色 */
html[data-theme='dark'] .recent-post-info .content {
    color: rgba(255, 255, 255, 0.8) !important;
}

body {
    background-color: transparent !important;
}

#footer {
    background: rgba(255,255,255,.15);
    color: #000;
    border-top-right-radius: 20px;
    border-top-left-radius: 20px;
    backdrop-filter: saturate(100%) blur(5px);
}

/* 暗黑模式下的页脚 */
html[data-theme='dark'] #footer {
    background: rgba(18,18,18,.7);
    color: rgba(255,255,255,.8);
}

#footer::before {
    background: rgba(255,255,255,.15);
}

html[data-theme='dark'] #footer::before {
    background: rgba(18,18,18,.7);
}

#footer #footer-wrap {
    color: var(--font-color);
}

#footer #footer-wrap a {
    color: var(--font-color);
}

/* 调整SVG社交图标样式 */
.social-icon img {
    height: 1.5em;
    width: 1.5em;
    vertical-align: middle;
    padding: 2px;
}

/* 暗黑模式下的SVG社交图标亮度提高 */
html[data-theme='dark'] .social-icon img {
    filter: brightness(1.8);
}

