# Hexo Configuration
## Docs: https://hexo.io/docs/configuration.html
## Source: https://github.com/hexojs/hexo/

# Site
title: 怜花辞客のBlog
subtitle: '愿你出走半生，归来仍是少年'
description: ''
keywords: "怜花辞客,博客,python,Blog,Hexo"
author: 怜花辞客
language: zh-CN
timezone: 'Asia/Shanghai'

# URL
## Set your site url here. For example, if you use GitHub Page, set url as 'https://username.github.io/project'
url: https://blog.lianhua.me
permalink: posts/:abbrlink/     # 可改为你需要的连接, 例如archives/:abbrlink.html
# abbrlink 配置
abbrlink:
  alg: crc32      # 算法--支持 crc16(默认) 和 crc32
  rep: hex        # 进制--支持 dec(默认/十进制) and hex(十六进制)
  drafts: false   # (true)处理草稿/(false)默认不处理草稿
permalink_defaults:
pretty_urls:
  trailing_index: true # Set to false to remove trailing 'index.html' from permalinks
  trailing_html: true # Set to false to remove trailing '.html' from permalinks

# Directory
source_dir: source
public_dir: public
tag_dir: tags
archive_dir: archives
category_dir: categories
code_dir: downloads/code
i18n_dir: :lang
skip_render:

# Writing
new_post_name: :title.md # File name of new posts
default_layout: post
titlecase: false # Transform title into titlecase
external_link:
  enable: true # Open external links in new tab
  field: site # Apply to the whole site
  exclude: ''
filename_case: 0
render_drafts: false
post_asset_folder: false
relative_link: false
future: true
highlight:
  enable: true
  line_number: true
  auto_detect: false
  tab_replace: ''
  wrap: true
  hljs: false
prismjs:
  enable: false
  preprocess: true
  line_number: true
  tab_replace: ''

# Home page setting
# path: Root path for your blogs index page. (default = '')
# per_page: Posts displayed per page. (0 = disable pagination)
# order_by: Posts order. (Order by date descending by default)
index_generator:
  path: ''
  per_page: 10
  order_by: -date

# Category & Tag
default_category: uncategorized
category_map:
tag_map:

# Metadata elements
## https://developer.mozilla.org/en-US/docs/Web/HTML/Element/meta
meta_generator: true

# Date / Time format
## Hexo uses Moment.js to parse and display date
## You can customize the date format as defined in
## http://momentjs.com/docs/#/displaying/format/
date_format: YYYY-MM-DD
time_format: HH:mm:ss
## updated_option supports 'mtime', 'date', 'empty'
updated_option: 'mtime'

# Pagination
## Set per_page to 0 to disable pagination
per_page: 10
pagination_dir: page

# Include / Exclude file(s)
## include:/exclude: options only apply to the 'source/' folder
include:
exclude:
ignore:

# Extensions
## Plugins: https://hexo.io/plugins/
## Themes: https://hexo.io/themes/
theme: butterfly

# 插件配置
plugins:

# Deployment
## Docs: https://hexo.io/docs/one-command-deployment
deploy:
- type: cjh_google_url_submitter
- type: cjh_bing_url_submitter
- type: cjh_baidu_url_submitter

hexo_submit_urls_to_search_engine:
  submit_condition: count #链接被提交的条件，可选值：count | period 现仅支持count
  count: 10 # 提交最新的10个链接
  period: 900 # 提交修改时间在 900 秒内的链接
  google: 1 # 是否向Google提交，可选值：1 | 0（0：否；1：是）
  bing: 1 # 是否向bing提交，可选值：1 | 0（0：否；1：是）
  baidu: 1 # 是否向baidu提交，可选值：1 | 0（0：否；1：是）
  txt_path: submit_urls.txt ## 文本文档名， 需要推送的链接会保存在此文本文档里
  baidu_host: https://blog.lianhua.me/ ## 在百度站长平台中注册的域名
  baidu_token: sVs8OUSTf4uhaGZG ## 请注意这是您的秘钥， 所以请不要把它直接发布在公众仓库里!
  bing_host: https://blog.lianhua.me/ ## 在bing站长平台中注册的域名
  bing_token: ******************************** ## 请注意这是您的秘钥， 所以请不要把它直接发布在公众仓库里!
  google_host: https://blog.lianhua.me/ ## 在google站长平台中注册的域名
  google_key_file: just-function-459912-k1-6a438e87f8d7.json #存放google key的json文件，放于网站根目录（与hexo _config.yml文件位置相同），请不要把json文件内容直接发布在公众仓库里!
  google_proxy: 0 # 向谷歌提交网址所使用的系统 http 代理，填 0 不使用 或http://127.0.0.1:7890
  replace: 0  # 是否替换链接中的部分字符串，可选值：1 | 0（0：否；1：是）
  find_what: 
  replace_with: 

nofollow:
  enable: true  #true/false 启用/关闭插件
  field: site   #site/post  处理全站/仅文章链接
  exclude:      #排除域名，同的子域名视为不同的域名
    - 'exclude1.com'
    - 'exclude2.com'


# hexo-safego安全跳转插件
# see https://blog.liushen.fun/posts/1dfd1f41/
hexo_safego:
  # 基本功能设置
  general:
    enable: true                # 启用插件
    enable_base64_encode: true  # 使用 Base64 编码
    enable_target_blank: true   # 打开新窗口
  # 安全设置
  security:
    url_param_name: 'u'         # URL 参数名
    html_file_name: 'go.html'   # 重定向页面的文件名
    ignore_attrs:               # 忽略处理的 HTML 属性
      - 'data-fancybox'
  # 容器与页面设置
  scope:
    apply_containers:           # 应用的容器选择器
      - '#article-container'
    apply_pages:                # 应用的页面路径
      - "/posts/"
      - "/devices/"
    exclude_pages:              # 排除的页面路径
  # 域名白名单
  whitelist:
    domain_whitelist:           # 允许的白名单域名
      - "qyliu.top"
      - "liushen.fun"
  # 页面外观设置
  appearance:
    avatar: https://blog.lianhua.me/image/lhck.png    # 头像路径
    title: "怜花辞客"            # 页面标题
    subtitle: "安全中心"         # 页面副标题
    darkmode: true              # 是否启用深色模式
    countdowntime: -1           # 倒计时秒数
  # 调试设置
  debug:
    enable: false               # 启用调试模式
